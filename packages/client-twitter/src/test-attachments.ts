/**
 * Test script to verify PNG image attachment functionality
 * This demonstrates how to use the updated sendStandardTweet method with PNG attachments
 *
 * IMPORTANT: All images should be stored locally on the server (either in generatedImages
 * folder for AI-generated images or in upload directories for user uploads).
 * The system no longer fetches images from HTTP URLs.
 */

import { Media } from "@elizaos/core";

// Example of how to create Media objects for PNG images stored locally on server
export function createLocalPngAttachment(
    localFilePath: string,
    title: string = "PNG Image",
    description: string = "A PNG image attachment"
): Media {
    return {
        id: `png-${Date.now()}`,
        url: localFilePath, // This should be the local file system path
        title,
        source: "local",
        description,
        text: `PNG image: ${title}`,
        contentType: "image/png",
    };
}

// Example for AI-generated images (stored in generatedImages folder)
export function createAIGeneratedPngAttachment(filename: string): Media {
    const filePath = `./generatedImages/${filename}`;
    return createLocalPngAttachment(
        filePath,
        "AI Generated PNG Image",
        `AI-generated PNG image: ${filename}`
    );
}

// Example for uploaded images (stored in uploads folder)
export function createUploadedPngAttachment(filename: string): Media {
    const filePath = `./uploads/${filename}`;
    return createLocalPngAttachment(
        filePath,
        "Uploaded PNG Image",
        `Uploaded PNG image: ${filename}`
    );
}

// Example of how to use with the Twitter client
export const exampleUsage = `
// Example 1: Posting with an AI-generated PNG file
const aiPngAttachment = createAIGeneratedPngAttachment('ai-image-123.png');
await twitterClient.sendStandardTweet(
    client,
    "Check out this AI-generated image!",
    undefined, // no reply
    [aiPngAttachment]
);

// Example 2: Posting with an uploaded PNG image
const uploadedPngAttachment = createUploadedPngAttachment('user-upload-456.png');
await twitterClient.sendStandardTweet(
    client,
    "Sharing this uploaded image!",
    undefined,
    [uploadedPngAttachment]
);

// Example 3: Posting with multiple PNG images from different sources
const attachments = [
    createAIGeneratedPngAttachment('ai-image-1.png'),
    createUploadedPngAttachment('user-image-2.png'),
    createLocalPngAttachment('./custom/path/image-3.png')
];
await twitterClient.sendStandardTweet(
    client,
    "Multiple PNG images in one tweet!",
    undefined,
    attachments
);
`;

export default {
    createLocalPngAttachment,
    createAIGeneratedPngAttachment,
    createUploadedPngAttachment,
    exampleUsage,
};
