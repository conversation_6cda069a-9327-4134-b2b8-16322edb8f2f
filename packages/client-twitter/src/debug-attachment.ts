/**
 * Debug script to test file attachment processing
 * Run this to verify that the image file exists and can be processed
 */

import * as fs from "fs";
import * as path from "path";

// Test the specific file from your log
const testFilePath = "/home/<USER>/dreamstarter-agent/agent/generatedImages/plan-1748794994390_1749013318169.png";

console.log("=== Debug Attachment Processing ===");
console.log(`Current working directory: ${process.cwd()}`);
console.log(`Test file path: ${testFilePath}`);

// Test 1: Check if file exists
console.log(`\n1. File exists check:`);
const fileExists = fs.existsSync(testFilePath);
console.log(`   File exists: ${fileExists}`);

if (!fileExists) {
    console.log(`   Checking directory: ${path.dirname(testFilePath)}`);
    try {
        const dirContents = fs.readdirSync(path.dirname(testFilePath));
        console.log(`   Directory contents: ${dirContents.join(", ")}`);
    } catch (error) {
        console.log(`   Error reading directory: ${error.message}`);
    }
} else {
    // Test 2: Get file stats
    console.log(`\n2. File stats:`);
    try {
        const stats = fs.statSync(testFilePath);
        console.log(`   File size: ${stats.size} bytes`);
        console.log(`   Is file: ${stats.isFile()}`);
        console.log(`   Modified: ${stats.mtime}`);
    } catch (error) {
        console.log(`   Error getting file stats: ${error.message}`);
    }

    // Test 3: Try to read file
    console.log(`\n3. File read test:`);
    try {
        const buffer = fs.readFileSync(testFilePath);
        console.log(`   Successfully read file: ${buffer.length} bytes`);
        
        // Check if it's a valid PNG
        const isPNG = buffer.length > 8 && 
                     buffer[0] === 0x89 && 
                     buffer[1] === 0x50 && 
                     buffer[2] === 0x4E && 
                     buffer[3] === 0x47;
        console.log(`   Is valid PNG: ${isPNG}`);
    } catch (error) {
        console.log(`   Error reading file: ${error.message}`);
    }
}

// Test 4: Test path resolution
console.log(`\n4. Path resolution test:`);
const resolvedPath = path.resolve(testFilePath);
console.log(`   Original path: ${testFilePath}`);
console.log(`   Resolved path: ${resolvedPath}`);
console.log(`   Paths match: ${testFilePath === resolvedPath}`);

// Test 5: Test relative path resolution
console.log(`\n5. Relative path test:`);
const relativePath = "./agent/generatedImages/plan-1748794994390_1749013318169.png";
const resolvedRelativePath = path.resolve(relativePath);
console.log(`   Relative path: ${relativePath}`);
console.log(`   Resolved relative: ${resolvedRelativePath}`);
console.log(`   Relative file exists: ${fs.existsSync(resolvedRelativePath)}`);

console.log("\n=== End Debug ===");
