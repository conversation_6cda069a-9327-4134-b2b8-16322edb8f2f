import { Request, Response } from "express";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    eliza<PERSON><PERSON>ger,
    twitter<PERSON>haracter,
    stringToUuid,
} from "@elizaos/core";
import { TwitterClientInterface } from "@elizaos/client-twitter";
import { IDirectClient } from "../models/types";

export const handleStartTwitterAgent = async (
    req: Request,
    res: Response,
    directClient: IDirectClient
): Promise<Response> => {
    const { projectName, projectId } = req.body;
    const agentId = stringToUuid("twitter-agent-" + projectName);

    // Create runtime with default character configuration
    const runtime: AgentRuntime = await directClient.startLinkedInAgent({
        ...twitterCharacter,
        name: projectName,
        id: agentId,
        projectId,
        username: projectName,
    });

    const rooms = await directClient.db.getRooms();
    const room = rooms.find((r: any) => r.id === runtime.agentId);

    let roomSettings = {};
    if (room && room.settings) {
        if (typeof room.settings === "string") {
            try {
                roomSettings = JSON.parse(room.settings);
            } catch (error) {
                elizaLogger.error("Failed to parse room settings:", error);
            }
        } else {
            roomSettings = room.settings;
        }
    }

    directClient.agents.set(runtime.agentId, runtime);

    try {
        await TwitterClientInterface.stop(runtime);
        const { url, manager: twitterClient } =
            await TwitterClientInterface.startWithCachedTokens(
                projectId,
                runtime
            );
        if (twitterClient) {
            if (roomSettings && room && room.status === "active") {
                console.log("SHOULD NOT BE HERE");
                runtime.clients.twitter = twitterClient;
                return res.status(200).json({ success: true });
            } else {
                runtime.clients.twitter = twitterClient;
                if (url) {
                    return res.status(200).json({ success: true, url });
                } else {
                    return res.status(200).json({ success: true });
                }
            }
        }

        directClient.unregisterAgent(directClient.agents.get(agentId));
        return res.status(403).json({
            success: false,
            message: "Failed to start client. Check your credentials.",
        });
    } catch (error) {
        console.log(error);
        directClient.unregisterAgent(directClient.agents.get(agentId));
        return res.status(403).json({
            success: false,
            message: "Failed to start client. Check your credentials.",
        });
    }

    // const {
    //     username,
    //     email,
    //     password: encryptedPassword,
    //     tokenAddress,
    //     ideaName,
    //     character: dynamicCharacter,
    // } = req.body;
    // const agentId = stringToUuid("new-agent-" + tokenAddress);
    // const password = decryptPassword(encryptedPassword);
    // // Process message examples if they exist
    // let messageExamples = [];
    // if (dynamicCharacter?.messageExamples?.length) {
    //     messageExamples = dynamicCharacter.messageExamples.map((example) =>
    //         example.map((e, index) => ({
    //             user: index === 0 ? "{{user1}}" : ideaName,
    //             content: {
    //                 text: e.content.text,
    //             },
    //         }))
    //     );
    // }

    // // Create runtime with character configuration
    // let runtime: AgentRuntime = await directClient.startTwitterAgent({
    //     ...defaultCharacter,
    //     name: ideaName,
    //     searchQuery: dynamicCharacter
    //         ? dynamicCharacter.searchQuery.join(" ")
    //         : "",
    //     id: agentId,
    //     username,
    //     settings: {
    //         secrets: {
    //             TWITTER_USERNAME: username,
    //             TWITTER_PASSWORD: encryptedPassword,
    //             TWITTER_EMAIL: email,
    //         },
    //     },
    //     system: dynamicCharacter?.system || defaultCharacter.system,
    //     bio: dynamicCharacter?.bio || defaultCharacter.bio,
    //     lore: dynamicCharacter?.lore || defaultCharacter.lore,
    //     messageExamples: dynamicCharacter
    //         ? messageExamples
    //         : defaultCharacter.messageExamples,
    //     postExamples:
    //         dynamicCharacter?.postExamples || defaultCharacter.postExamples,
    //     topics: dynamicCharacter?.topics || defaultCharacter.topics,
    //     style: dynamicCharacter?.style || defaultCharacter.style,
    //     adjectives: dynamicCharacter?.adjectives || defaultCharacter.adjectives,
    // });

    // directClient.agents.set(runtime.agentId, runtime);

    // const roomId = stringToUuid(req.body.roomId ?? "default-room-" + agentId);

    // // Find runtime if not available
    // if (!runtime) {
    //     runtime = Array.from(directClient.agents.values()).find(
    //         (a) => a.character.name.toLowerCase() === agentId.toLowerCase()
    //     );
    // }

    // if (!runtime) {
    //     res.status(404).send("Agent not found");
    //     return;
    // }

    // try {
    //     await runtime.ensureConnection(
    //         agentId,
    //         roomId,
    //         req.body.userName,
    //         req.body.name,
    //         "direct"
    //     );

    //     // Stop existing Twitter client if any
    //     await TwitterClientInterface.stop(runtime);

    //     // Start new Twitter client
    //     const { loginSuccess, manager: twitterClient }: any =
    //         await TwitterClientInterface.startExternal(
    //             runtime,
    //             email,
    //             username,
    //             password
    //         );

    //     if (twitterClient && loginSuccess) {
    //         runtime.clients.twitter = twitterClient;
    //         res.status(200).json({
    //             success: true,
    //             agentId: runtime.agentId,
    //         });
    //     } else {
    //         directClient.unregisterAgent(directClient.agents.get(agentId));
    //         res.status(403).json({
    //             success: false,
    //             message: "Failed to start client. Check your credentials.",
    //         });
    //     }
    // } catch (error) {
    //     console.log(error);
    //     directClient.unregisterAgent(directClient.agents.get(agentId));
    //     res.status(403).json({
    //         success: false,
    //         message: "Failed to start client. Check your credentials.",
    //     });
    // }
};

export const handleTwitterCallback = async (
    req: Request,
    res: Response,
    directClient: IDirectClient
) => {
    const oauth_token = req.query.oauth_token as string;
    const oauth_verifier = req.query.oauth_verifier as string;

    if (!oauth_token || !oauth_verifier) {
        elizaLogger.error("Missing required OAuth parameters");
        return res.redirect(
            `${process.env.FRONTEND_URL}/api/auth/callback/twitter?error=missing_oauth_params`
        );
    }

    try {
        const anyRuntime = Array.from(directClient.agents.values())[0];
        if (!anyRuntime) {
            elizaLogger.error("No agent runtimes available");
            return res.redirect(
                `${process.env.FRONTEND_URL}/api/auth/callback/twitter?error=no_agents_available`
            );
        }

        elizaLogger.log(
            `Attempting to retrieve OAuth data for token: ${oauth_token}`
        );

        const storedData = await anyRuntime.cacheManager?.get<{
            agentId: string;
            projectId: string;
            timestamp: number;
        }>(oauth_token);

        if (!storedData) {
            elizaLogger.error(
                `No stored OAuth data found for token: ${oauth_token}`
            );
            elizaLogger.error(
                "Available agents:",
                Array.from(directClient.agents.keys())
            );

            elizaLogger.error(
                "This might be due to cache expiration or agent mismatch"
            );

            return res.redirect(
                `${process.env.FRONTEND_URL}/api/auth/callback/twitter?error=oauth_data_not_found`
            );
        }

        elizaLogger.log(
            `Found OAuth data for token: ${oauth_token}, agentId: ${storedData.agentId}, projectId: ${storedData.projectId}`
        );

        const dataAge = Date.now() - storedData.timestamp;
        if (dataAge > 30 * 60 * 1000) {
            elizaLogger.error(
                `OAuth data is too old (${Math.round(dataAge / 1000 / 60)} minutes), rejecting`
            );
            return res.redirect(
                `${process.env.FRONTEND_URL}/api/auth/callback/twitter?error=oauth_data_expired`
            );
        }

        const { agentId, projectId } = storedData;
        const runtime: AgentRuntime = directClient.agents.get(agentId);

        if (!runtime) {
            elizaLogger.error(
                `Agent runtime not found for agent ID: ${agentId}`
            );
            return res.redirect(
                `${process.env.FRONTEND_URL}/api/auth/callback/twitter?projectId=${projectId}&error=agent_not_found`
            );
        }

        const twitterClient = runtime.clients.twitter;
        if (!twitterClient) {
            elizaLogger.error(
                `Twitter client not found for agent ID: ${agentId}`
            );
            return res.redirect(
                `${process.env.FRONTEND_URL}/api/auth/callback/twitter?projectId=${projectId}&error=twitter_client_not_found`
            );
        }

        const { accessToken, accessSecret, username } =
            await TwitterClientInterface.completeOAuth(
                runtime,
                oauth_token,
                oauth_verifier
            );

        runtime.character = {
            ...runtime.character,
            settings: {
                ...runtime.character.settings,
                secrets: {
                    ...runtime.character.settings?.secrets,
                    TWITTER_ACCESS_TOKEN: accessToken,
                    TWITTER_ACCESS_SECRET: accessSecret,
                    TWITTER_USERNAME: username,
                },
            },
        };

        try {
            twitterClient.client.twitterClient =
                twitterClient.client.oauthService.getScraper();

            twitterClient.client.profile =
                await twitterClient.client.fetchProfile(username);

            elizaLogger.debug(
                "Fetched profile:",
                JSON.stringify(twitterClient.client.profile, null, 2)
            );

            if (twitterClient.client.profile?.id) {
                elizaLogger.info(
                    `Successfully authenticated Twitter user: ${username} (ID: ${twitterClient.client.profile.id})`
                );

                if (!twitterClient.client.profile.username) {
                    elizaLogger.warn(
                        "Profile missing username, setting it manually"
                    );
                    twitterClient.client.profile.username = username;
                }

                elizaLogger.debug("Starting Twitter post client...");

                // Update room status in database with the new character settings
                await directClient.db.updateRoomStatus(
                    runtime.agentId,
                    "active",
                    JSON.stringify({ ...runtime.character }),
                    JSON.stringify({
                        schedulingPosts: false,
                        postInterval: 0,
                        // Also save Twitter tokens to room settings for server-side initialization
                        TWITTER_ACCESS_TOKEN: accessToken,
                        TWITTER_ACCESS_SECRET: accessSecret,
                        TWITTER_USERNAME: username,
                    })
                );
            } else {
                elizaLogger.error(
                    "Failed to fetch Twitter profile after OAuth authentication"
                );
                return res.redirect(
                    `${process.env.FRONTEND_URL}/api/auth/callback/twitter?projectId=${projectId}&error=authentication_failed`
                );
            }
        } catch (error) {
            elizaLogger.error("Twitter authentication error:", error);
            return res.redirect(
                `${process.env.FRONTEND_URL}/api/auth/callback/twitter?projectId=${projectId}&error=authentication_failed`
            );
        }

        await runtime.cacheManager?.delete(oauth_token);

        const authenticatedUsername = username || "unknown";

        return res.redirect(
            `${process.env.FRONTEND_URL}/api/auth/callback/twitter?projectId=${projectId}&success=true&agentId=${agentId}&username=${authenticatedUsername}`
        );
    } catch (error) {
        elizaLogger.error("Twitter authentication error:", error);
        return res.redirect(
            `${process.env.FRONTEND_URL}/api/auth/callback/twitter?error=authentication_failed`
        );
    }
};
